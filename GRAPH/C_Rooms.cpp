#include<bits/stdc++.h>
using namespace std;
#define ll long long
int dr[4] = {0, -1, 0, 1};
int dc[4] = {-1, 0, 1, 0};
typedef pair<int, int> pii;
int bfs(int r, int c)
{
    queue<pii> Q;
    Q.push(r, c);
    mark[]
}
int main()
{
    ll n, m;
    scanf("%lld", n);
    scanf("%lld", m);
    vector<vector<char>> building(n, vector<ll>(m));
    for(int i = 0; i < n; i++)
    {
        for(int j = 0; s < m; j++)
        {
            scanf("%c", building[i][j]);
        }
    }
    int res = 0;
    for(int i = 0; i < n; i++)
    {
        for(int j = 0; j < m; j++)
        {
            if(a[i][j] == '#')continue;

        }
    }
}
