{"name": "Increasing Array", "group": "CSES - CSES Problem Set", "url": "https://cses.fi/problemset/task/1094", "interactive": false, "memoryLimit": 512, "timeLimit": 1000, "tests": [{"id": 1743150970727, "input": "5\n3 2 5 1 7\n", "output": "5\n"}, {"id": 1743151270744, "input": "10\n1000000000 1 1 1 1 1 1 1 1 1", "output": "8999999991"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "IncreasingArray"}}, "batch": {"id": "5c608bfa-9438-47f0-8646-4f9c8e50a3b9", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\CSES\\INTRODUCTORY\\Increasing_Array.cpp"}