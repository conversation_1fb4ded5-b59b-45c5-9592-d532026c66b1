{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "c:/C-COMPETITIVE-TRAINING/NNTAN_PRE_HCMTST/PrefixSum_BinSearch_DiffArray", "program": "c:/C-COMPETITIVE-TRAINING/NNTAN_PRE_HCMTST/PrefixSum_BinSearch_DiffArray/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}