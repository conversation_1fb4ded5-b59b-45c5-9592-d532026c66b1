{"name": "Matrix Zigzag", "group": "MarisaOJ - Two dimensional array", "url": "https://marisaoj.com/problem/434", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1744727599007, "input": "3 3\n1 2 3\n4 5 6\n7 8 9\n", "output": "1 2 4 7 5 3 6 8 9\n"}, {"id": 1744728456343, "input": "3 4\n3 8 5 8\n2 7 4 3\n6 1 9 5", "output": "3 8 2 6 7 5 8 4 1 9 3 5"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "MatrixZigzag"}}, "batch": {"id": "49599c7a-2812-4a48-a2a4-81412c1019a0", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL0\\2D\\Matrix_Zigzag.cpp"}