{"name": "Point coverage", "group": "MarisaOJ - Two dimensional array", "url": "https://marisaoj.com/problem/40", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1744355453956, "input": "2\n1 1 2 2\n1 1 3 3\n", "output": "9\n"}, {"id": 1744727524398, "input": "2\n3 3 5 7\n4 4 10 10", "output": "56"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "PointCoverage"}}, "batch": {"id": "0a5558cc-4027-455f-9d42-24ab6f8c1762", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL0\\2D\\Point_coverage.cpp"}