{"name": "Spiral matrix", "group": "MarisaOJ - Two dimensional array", "url": "https://marisaoj.com/problem/407", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1744355364642, "input": "3 3", "output": "1 2 3 \n8 9 4 \n7 6 5"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "SpiralMatrix"}}, "batch": {"id": "5eb015fa-56af-4d06-a263-0399daace43f", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL0\\2D\\Spiral_matrix.cpp"}