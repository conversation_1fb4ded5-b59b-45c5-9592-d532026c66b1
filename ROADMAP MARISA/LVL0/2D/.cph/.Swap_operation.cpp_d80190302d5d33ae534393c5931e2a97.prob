{"name": "Swap operation", "group": "MarisaOJ - Two dimensional array", "url": "https://marisaoj.com/problem/41", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1744356662232, "input": "2 2 2\n1 2\n3 4\n1 1 2\n2 1 2\n", "output": "4 3\n2 1\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "SwapOperation"}}, "batch": {"id": "873e1190-4abe-4214-9a70-30a05dacc83a", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL0\\2D\\Swap_operation.cpp"}