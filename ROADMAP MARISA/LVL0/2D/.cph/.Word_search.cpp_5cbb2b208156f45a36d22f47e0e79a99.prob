{"name": "Word search", "group": "MarisaOJ - Two dimensional array", "url": "https://marisaoj.com/problem/433", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1744359493417, "input": "6 4\nmari\naris\nrisa\nisam\nsama\namar\nmarisa\n", "output": "YES\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "WordSearch"}}, "batch": {"id": "4bd861c0-2c2e-4a7f-8a32-c4652e1bf42c", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL0\\2D\\Word_search.cpp"}