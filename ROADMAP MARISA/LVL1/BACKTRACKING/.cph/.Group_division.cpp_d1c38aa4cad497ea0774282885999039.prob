{"name": "Group division", "group": "MarisaOJ - Backtracking", "url": "https://marisaoj.com/problem/545", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1741943615997, "input": "5 3\n1 4 6 9 10\n", "output": "1 3 3 1 2\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "GroupDivision"}}, "batch": {"id": "7f348aed-1462-45a2-ad98-80caacbd4f75", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL1\\BACKTRACKING\\Group_division.cpp"}