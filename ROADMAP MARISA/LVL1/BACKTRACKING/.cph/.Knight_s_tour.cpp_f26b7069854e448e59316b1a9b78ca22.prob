{"name": "<PERSON>'s tour", "group": "MarisaOJ - Backtracking", "url": "https://marisaoj.com/problem/324", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1742087327513, "input": "5 5\n", "output": "  1   6  15  10  21\n 14   9  20   5  16\n 19   2   7  22  11\n  8  13  24  17   4\n 25  18   3  12  23\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "KnightsTour"}}, "batch": {"id": "410189dd-8fc0-48e1-9a4e-1bcd637c396d", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL1\\BACKTRACKING\\Knight_s_tour.cpp"}