{"name": "Maximum path", "group": "MarisaOJ - Backtracking", "url": "https://marisaoj.com/problem/55", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1742211963593, "input": "2 2 2 1\n1 1 2 1\n1 1 2 1\n1 1 2 2\n", "output": "14\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "MaximumPath"}}, "batch": {"id": "9abd84cd-f3b1-447b-9eef-8fccec035ddf", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL1\\BACKTRACKING\\Maximum_path.cpp"}