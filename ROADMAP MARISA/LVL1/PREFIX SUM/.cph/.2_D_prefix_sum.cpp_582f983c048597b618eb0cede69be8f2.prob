{"name": "2D prefix sum", "group": "MarisaOJ - Introduction to Prefix sum", "url": "https://marisaoj.com/problem/66", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "2 3 2\n1 4 3\n2 2 3\n1 1 2 2\n2 1 2 3\n", "output": "9\n7\n", "id": 1742313425276}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DPrefixSum"}}, "batch": {"id": "c3c3ba1a-1ece-4356-b4a8-f8583dc807d8", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL1\\PREFIX SUM\\2_D_prefix_sum.cpp"}