{"name": "Divisible by d", "group": "MarisaOJ - Introduction to Prefix sum", "url": "https://marisaoj.com/problem/62", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1742309654776, "input": "5 4\n1 3 -2 3 -5\n", "output": "4\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DivisibleByD"}}, "batch": {"id": "87f4d4c2-497b-41e8-aab0-e79302180a3a", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL1\\PREFIX SUM\\Divisible_by_d.cpp"}