{"name": "Game on array", "group": "MarisaOJ - Introduction to Prefix sum", "url": "https://marisaoj.com/problem/64", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1742311006353, "input": "4\n1 1 1 -1\n", "output": "4\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "GameOnArray"}}, "batch": {"id": "24554b68-dee9-4fdf-b8d1-20ae29f814d4", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL1\\PREFIX SUM\\Game_on_array.cpp"}