{"name": "Stair query", "group": "MarisaOJ - Introduction to Prefix sum", "url": "https://marisaoj.com/problem/320", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1742353913323, "input": "3 2\n-1 3 2\n1 3\n2 3\n", "output": "11\n7\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "batch": {"id": "0c24b7c0-94d8-4386-9321-f0a9601f80ca", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL1\\PREFIX SUM\\Stair_query.cpp"}