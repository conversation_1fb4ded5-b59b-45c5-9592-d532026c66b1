{"name": "<PERSON><PERSON>cing <PERSON> Together", "group": "Codeforces - April Fools Day Contest 2025", "url": "https://codeforces.com/problemset/problem/2095/A", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "APiecingItTogether"}}, "batch": {"id": "369f5817-2d70-48ca-9ddf-b28ccc5c8011", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\2POINTER\\A_Piecing_It_Together.cpp"}