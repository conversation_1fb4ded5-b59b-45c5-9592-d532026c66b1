{"name": "Brewing potion 3", "group": "MarisaOJ - Introduction to two pointers", "url": "https://marisaoj.com/problem/95", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1745768265550, "input": "6 3\n1 2 5 7 9 10\n", "output": "5\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BrewingPotion3"}}, "batch": {"id": "6f68559e-5535-4c5a-ac9d-5d4a553d96c4", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\2POINTER\\Brewing_potion_3.cpp"}