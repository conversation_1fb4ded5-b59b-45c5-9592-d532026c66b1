{"name": "Merge array", "group": "MarisaOJ - Introduction to two pointers", "url": "https://marisaoj.com/problem/92", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1745245006855, "input": "3\n1 3 4\n1 2 3\n", "output": "1 1 2 3 3 4\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "batch": {"id": "fe6dd5a0-3e92-4758-ba41-573cd2501e08", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\2POINTER\\Merge_array.cpp"}