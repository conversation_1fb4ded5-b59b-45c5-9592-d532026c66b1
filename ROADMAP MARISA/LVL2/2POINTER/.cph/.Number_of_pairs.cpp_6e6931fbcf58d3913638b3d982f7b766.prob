{"name": "Number of pairs", "group": "MarisaOJ - Introduction to two pointers", "url": "https://marisaoj.com/problem/100", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1745336042235, "input": "5 6\n1 2 3 4 5\n", "output": "2\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "NumberOfPairs"}}, "batch": {"id": "a5dbd6e2-3fdb-41e6-af76-948ef76d6a19", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\2POINTER\\Number_of_pairs.cpp"}