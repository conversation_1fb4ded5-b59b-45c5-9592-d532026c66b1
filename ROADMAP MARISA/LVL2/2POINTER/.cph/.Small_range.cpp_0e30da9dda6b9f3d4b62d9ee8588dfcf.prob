{"name": "Small range", "group": "MarisaOJ - Introduction to two pointers", "url": "https://marisaoj.com/problem/97", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1745332388425, "input": "4 1\n1 3 1 2\n", "output": "5\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "SmallRange"}}, "batch": {"id": "dbd628f3-739b-4d3f-9d50-e931d0379fc8", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\2POINTER\\Small_range.cpp"}