{"name": "Sum of three values", "group": "MarisaOJ - Introduction to two pointers", "url": "https://marisaoj.com/problem/93", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "4 8\n1 2 5 7\n", "output": "1 2 3\n", "id": 1745373211601}, {"id": 1745373834129, "input": "8 13\n5 5 9 5 6 5 6 3", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "SumOfThreeValues"}}, "batch": {"id": "90635a8e-763e-42d2-a4e2-62445c416f76", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\2POINTER\\Sum_of_three_values.cpp"}