{"name": "Unique elements", "group": "MarisaOJ - Introduction to two pointers", "url": "https://marisaoj.com/problem/96", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1745247548688, "input": "4\n1 2 1 2\n", "output": "7\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "UniqueElements"}}, "batch": {"id": "dc22da76-b3cb-4e0a-bf0c-00cc3dc7c705", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\2POINTER\\Unique_elements.cpp"}