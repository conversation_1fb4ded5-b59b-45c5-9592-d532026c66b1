{"name": "Consecutive integers", "group": "MarisaOJ - Introduction to Binary Search", "url": "https://marisaoj.com/problem/197", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1744446261558, "input": "3\n4 10 5\n", "output": "1"}, {"id": 1744446709761, "input": "3\n12 14 12", "output": "1"}, {"id": 1744446712621, "input": "9\n7 10 11 8 11 6 6 7 9", "output": "3"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "ConsecutiveIntegers"}}, "batch": {"id": "0c5d2bd8-3c2f-4881-901e-b485596be0b1", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\BINSEARCH\\Consecutive_integers.cpp"}