{"name": "Counting pairs", "group": "MarisaOJ - Introduction to Binary Search", "url": "https://marisaoj.com/problem/80", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1744376473622, "input": "3 2 4\n1 2 3\n", "output": "2\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CountingPairs"}}, "batch": {"id": "b86c2302-d604-4bbe-a2fb-bbf3c68865f7", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\BINSEARCH\\Counting_pairs.cpp"}