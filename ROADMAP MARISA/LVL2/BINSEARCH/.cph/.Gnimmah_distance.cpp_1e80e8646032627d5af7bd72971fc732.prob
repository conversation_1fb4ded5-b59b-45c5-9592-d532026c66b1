{"name": "Gnimmah distance", "group": "MarisaOJ - Introduction to Binary Search", "url": "https://marisaoj.com/problem/83", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1744472635701, "input": "aaba\nabc\n", "output": "3\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "GnimmahDistance"}}, "batch": {"id": "972cb9e5-3c9e-4f3d-a2a9-eb33d19629bb", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\BINSEARCH\\Gnimmah_distance.cpp"}