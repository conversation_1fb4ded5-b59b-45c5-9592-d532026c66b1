{"name": "Hamming number", "group": "MarisaOJ - Introduction to Binary Search", "url": "https://marisaoj.com/problem/82", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1744385497882, "input": "5\n1\n2\n3\n4\n7\n", "output": "1\n2\n3\n4\n-1\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "HammingNumber"}}, "batch": {"id": "0a72485a-f303-4ddc-a8b4-9e3527984b8b", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\BINSEARCH\\Hamming_number.cpp"}