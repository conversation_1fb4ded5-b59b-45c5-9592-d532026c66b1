{"name": "The k-th candy", "group": "MarisaOJ - Introduction to Binary Search", "url": "https://marisaoj.com/problem/81", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1744376932272, "input": "3 3\n2 2\n1 1\n3 3\n1\n3\n5\n", "output": "1\n2\n3\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "TheKThCandy"}}, "batch": {"id": "8c4ee17b-9dd6-4832-b54e-3fc982641849", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\BINSEARCH\\The_k_th_candy.cpp"}