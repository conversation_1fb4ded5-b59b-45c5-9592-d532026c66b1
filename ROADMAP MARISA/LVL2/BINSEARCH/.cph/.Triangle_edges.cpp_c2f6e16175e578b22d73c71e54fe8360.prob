{"name": "Triangle edges", "group": "MarisaOJ - Introduction to Binary Search", "url": "https://marisaoj.com/problem/85", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "4\n1 2 3 4\n", "output": "1\n", "id": 1744384088737}, {"id": 1744385315784, "input": "6\n2 6 9 7 6 2", "output": "10"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "TriangleEdges"}}, "batch": {"id": "6e7da08e-023d-4fdc-94ad-7974d922cc66", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\BINSEARCH\\Triangle_edges.cpp"}