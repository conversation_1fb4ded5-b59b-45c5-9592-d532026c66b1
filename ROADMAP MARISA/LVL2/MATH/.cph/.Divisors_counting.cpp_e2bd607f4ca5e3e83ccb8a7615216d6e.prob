{"name": "Divisors counting", "group": "MarisaOJ - Basic number theory", "url": "https://marisaoj.com/problem/106", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "1\n1 4\n", "output": "8\n", "id": 1746007248116}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DivisorsCounting"}}, "batch": {"id": "b2597201-ca78-4d7a-b8bf-f6e176fac7f6", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\MATH\\Divisors_counting.cpp"}