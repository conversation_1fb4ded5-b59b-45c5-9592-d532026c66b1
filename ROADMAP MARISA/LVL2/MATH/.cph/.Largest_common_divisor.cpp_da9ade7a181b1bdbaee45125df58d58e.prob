{"name": "Largest common divisor", "group": "MarisaOJ - Basic number theory", "url": "https://marisaoj.com/problem/107", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1746007706628, "input": "4\n1 3 4 5", "output": "60\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "LargestCommonDivisor"}}, "batch": {"id": "fd0024f0-8241-4cad-baaa-3e7cdc8e4618", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\MATH\\Largest_common_divisor.cpp"}