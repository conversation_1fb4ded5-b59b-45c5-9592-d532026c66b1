{"name": "Maximum GCD", "group": "MarisaOJ - Basic number theory", "url": "https://marisaoj.com/problem/340", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1746004832063, "input": "4\n3 6 5 2\n", "output": "3\n"}, {"id": 1746006643920, "input": "7\n10 15 10 1 11 20 20 ", "output": "20"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "MaximumGCD"}}, "batch": {"id": "73a09d18-c39c-4ed4-bf19-c739219d95e2", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\MATH\\Maximum_GCD.cpp"}