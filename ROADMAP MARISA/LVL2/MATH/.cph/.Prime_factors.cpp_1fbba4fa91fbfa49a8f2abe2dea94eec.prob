{"name": "Prime factors", "group": "MarisaOJ - Basic number theory", "url": "https://marisaoj.com/problem/104", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1746003707006, "input": "2\n9\n24\n", "output": "3\n2 3\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "PrimeFactors"}}, "batch": {"id": "58577e1f-024a-422a-895c-5a9d4d2e6d05", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\MATH\\Prime_factors.cpp"}