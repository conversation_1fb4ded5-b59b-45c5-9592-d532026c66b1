{"name": "Prime number 2", "group": "MarisaOJ - Basic number theory", "url": "https://marisaoj.com/problem/25", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1745998965196, "input": "7\n", "output": "YES\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "PrimeNumber2"}}, "batch": {"id": "9bf63d9d-ba17-42eb-9aba-0746c2bb24aa", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\MATH\\Prime_number_2.cpp"}