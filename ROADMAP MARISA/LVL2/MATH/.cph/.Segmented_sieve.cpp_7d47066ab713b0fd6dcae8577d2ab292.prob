{"name": "Segmented sieve", "group": "MarisaOJ - Basic number theory", "url": "https://marisaoj.com/problem/103", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1745999680732, "input": "20 30\n", "output": "23 29\n"}, {"id": 1746001396506, "input": "10 20", "output": "11 13 17 19"}, {"id": 1746001424305, "input": "54 97", "output": "59 61 67 71 73 79 83 89 97"}, {"id": 1746001561752, "input": "84 100", "output": "89 97"}, {"id": 1746003272942, "input": "1 123123", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "SegmentedSieve"}}, "batch": {"id": "5ba0c6b2-4377-4b00-bcc4-de869a7b649f", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\MATH\\Segmented_sieve.cpp"}