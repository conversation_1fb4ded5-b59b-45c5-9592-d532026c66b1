{"name": "Sieve of Eratosthenes", "group": "MarisaOJ - Basic number theory", "url": "https://marisaoj.com/problem/102", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "15\n", "output": "2 3 5 7 11 13\n", "id": 1745999248508}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "SieveOfEratosthenes"}}, "batch": {"id": "73886a93-26e4-4dc5-99ff-87a1deb8efc8", "size": 1}, "srcPath": "c:\\C-COMPETITIVE-TRAINING\\ROADMAP MARISA\\LVL2\\MATH\\Sieve_of_Eratosthenes.cpp"}