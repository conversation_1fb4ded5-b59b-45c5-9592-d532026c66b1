#include <bits/stdc++.h>
using namespace std;
#define int long long
#define ll long long
#define KING_PHAT signed main()
#define fast ios_base::sync_with_stdio(0); cin.tie(0); cout.tie(0);
#define endl '\n'
#define vec(type, a, size, value) vector<type> a(size, value);
#define all(x) x.begin(), x.end()
#define I_O(input_name, output_name) freopen((string(input_name) + ".inp").c_str(), "r", stdin); freopen((string(output_name) + ".out").c_str(), "w", stdout); fast;
#define min(a,b) min((ll)a, (ll)b)
#define max(a,b) max((ll)a, (ll)b)
bool multitest = false;
void init()
{
    freopen("input.inp", "r", stdin);
    freopen("output.out", "w", stdout);
    fast;
}
// able for <= 1e16
bool isprime(int x)
{
    if(x <= 1)return false;
    else if(x == 2 || x == 3 || x == 5)return true;
    else if(x % 2 == 0 || x % 3 == 0 || x % 5 == 0)return false;
    for(int i = 5; i * i <= x; i++)
    {
        if(x%i == 0)return false;
    }
    return true;
}
void sol()
{
    int n;
    cin>>n;
    if(isprime(n))cout<<"YES";
    else cout<<"NO";
}
KING_PHAT
{
    multitest = false;
    // set state for multitest
    // multitest = true;
    //set state for I/O style
    //I_O("test1", "test1");
    fast;
    if (multitest)
    {
        int t;
        cin >> t;
        while (t--)
        {
            sol();
        }
    }
    else
    {
        sol();
    }
    return 0;
}